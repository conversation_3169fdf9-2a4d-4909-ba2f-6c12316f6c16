#include "../include/types.h"
#include "../include/defs.h"
#include "../include/elf.h"
#include "../include/proc.h"
#include "../include/memlayout.h"

// 加载ELF文件到进程
int elf_load(struct proc *p, void *binary, uint size)
{
    struct elfhdr *elf;
    struct proghdr *ph, *eph;
    char *addr;
    uint i;

    if (p == 0 || binary == 0 || size == 0)
        return -1;

    elf = (struct elfhdr *)binary;

    // 检查ELF魔数
    if (elf->magic != ELF_MAGIC)
    {
        cprintf("Invalid ELF magic: 0x%x\n", elf->magic);
        return -1;
    }

    // 获取程序头表
    ph = (struct proghdr *)((char *)binary + elf->phoff);
    eph = ph + elf->phnum;

    // 计算加载地址（每个进程有自己的地址空间）
    uint load_base = USERBASE + (p - procs) * PROGSIZE;

    cprintf("Loading ELF: entry=0x%x, phnum=%d, load_base=0x%x\n",
            elf->entry, elf->phnum, load_base);

    // 遍历程序头，加载可加载段
    for (; ph < eph; ph++)
    {
        if (ph->type != ELF_PROG_LOAD)
            continue;

        if (ph->memsz < ph->filesz)
        {
            cprintf("ELF: memsz < filesz\n");
            return -1;
        }

        if (ph->vaddr + ph->memsz < ph->vaddr)
        {
            cprintf("ELF: vaddr overflow\n");
            return -1;
        }

        // 计算实际加载地址
        addr = (char *)(load_base + ph->vaddr - 0x200000); // 减去链接地址偏移

        cprintf("Loading segment: vaddr=0x%x, filesz=%d, memsz=%d, addr=0x%x\n",
                ph->vaddr, ph->filesz, ph->memsz, (uint)addr);

        // 检查是否超出进程空间
        if ((uint)addr + ph->memsz > load_base + PROGSIZE)
        {
            cprintf("ELF: segment too large\n");
            return -1;
        }

        // 复制文件内容
        char *src = (char *)binary + ph->off;
        for (i = 0; i < ph->filesz; i++)
        {
            addr[i] = src[i];
        }

        // 清零BSS段
        for (i = ph->filesz; i < ph->memsz; i++)
        {
            addr[i] = 0;
        }
    }

    // 设置进程信息
    p->entry = load_base + (elf->entry - 0x200000); // 调整入口地址
    p->size = size;
    p->state = RUNNABLE;

    cprintf("ELF loaded successfully: entry=0x%x\n", p->entry);
    return 0;
}
